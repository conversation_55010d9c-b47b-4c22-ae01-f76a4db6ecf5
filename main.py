"""BoopBoard - a stream deck-style app, built with NiceGUI.

This app is a simple, customizable, and easy to use macroboard.
"""

from nicegui import native, ui


@ui.page("/")
def main() -> None:
    """Main page of the app."""
    is_host = ui.context.client.ip == "127.0.0.1"
    fullscreen = ui.fullscreen()

    if not is_host:
        # Prevent pull-to-refresh and overscroll on touch
        ui.add_css("""
        body, html, #nicegui-body {
            overflow: hidden !important;
            position: fixed !important;
            width: 100% !important;
            height: 100% !important;
        }

        body {
            overscroll-behavior: none !important;
        }
        """)

        if not fullscreen.value:

            def _make_fullscreen() -> None:
                fullscreen.enter()
                dialog.close()

            with ui.dialog() as dialog, ui.card():
                ui.label("Please go full screen to use this app.")
                ui.button("OK", on_click=_make_fullscreen)
            dialog.open()

    with ui.element("div").classes("flex w-full h-screen overflow-hidden"):
        # Layer Sidebar
        with ui.element("div").classes(
            "w-16 bg-base-300 p-2 flex flex-col items-center justify-center gap-4"
        ):
            # Default Layer
            ui.icon("view_quilt").classes("text-4xl text-white")

        # Main Content
        with (
            ui.element("div").classes(
                "grow bg-base-100 pb-4 flex flex-col overflow-hidden justify-items-center"
            ),
            ui.grid(rows=4, columns=4).classes("w-full h-full gap-2 p-4"),
        ):
            for i in range(1, 17):  # 4x4 = 16 buttons
                ui.button(f"Button {i}").classes("w-full h-full min-h-0")


ui.run(
    port=native.find_open_port(),  # Finds an open port for the app to use
    dark=None,
    title="BoopBoard",
    favicon="🗿",
)
