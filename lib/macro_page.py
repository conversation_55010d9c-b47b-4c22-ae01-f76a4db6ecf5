"""BoopBoard - a stream deck-style app, built with NiceGUI.

This app is a simple, customizable, and easy to use macroboard.
"""

from nicegui import ui


def macro_page() -> None:
    """Main page of the app that the client will see."""
    fullscreen = ui.fullscreen()

    # Prevent pull-to-refresh and overscroll on touch
    ui.add_css("""
    body, html, #nicegui-body {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        height: 100% !important;
    }

    body {
        overscroll-behavior: none !important;
    }
    """)

    if not fullscreen.value:

        def _make_fullscreen() -> None:
            fullscreen.enter()
            dialog.close()

        with ui.dialog() as dialog, ui.card():
            ui.label("Please go full screen to use this app.")
            ui.button("OK", on_click=_make_fullscreen)
        dialog.open()
